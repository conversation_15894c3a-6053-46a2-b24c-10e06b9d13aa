#include "ast.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

// Create program node
ASTNode* ast_create_program() {
    ASTNode *node = malloc(sizeof(ASTNode));
    if (!node) return NULL;
    
    node->type = AST_PROGRAM;
    node->data.program.statements = NULL;
    node->data.program.statement_count = 0;
    node->data.program.statement_capacity = 0;
    
    return node;
}

// Create declaration node
ASTNode* ast_create_declaration(const char *identifier) {
    ASTNode *node = malloc(sizeof(ASTNode));
    if (!node) return NULL;
    
    node->type = AST_DECLARATION;
    node->data.declaration.identifier = malloc(strlen(identifier) + 1);
    if (!node->data.declaration.identifier) {
        free(node);
        return NULL;
    }
    strcpy(node->data.declaration.identifier, identifier);
    
    return node;
}

// Create assignment node
ASTNode* ast_create_assignment(const char *identifier, ASTNode *expression) {
    ASTNode *node = malloc(sizeof(ASTNode));
    if (!node) return NULL;
    
    node->type = AST_ASSIGNMENT;
    node->data.assignment.identifier = malloc(strlen(identifier) + 1);
    if (!node->data.assignment.identifier) {
        free(node);
        return NULL;
    }
    strcpy(node->data.assignment.identifier, identifier);
    node->data.assignment.expression = expression;
    
    return node;
}

// Create conditional node
ASTNode* ast_create_conditional(ASTNode *condition, ASTNode **statements, int count) {
    ASTNode *node = malloc(sizeof(ASTNode));
    if (!node) return NULL;
    
    node->type = AST_CONDITIONAL;
    node->data.conditional.condition = condition;
    node->data.conditional.statement_count = count;
    node->data.conditional.statement_capacity = count;
    
    if (count > 0) {
        node->data.conditional.statements = malloc(sizeof(ASTNode*) * count);
        if (!node->data.conditional.statements) {
            free(node);
            return NULL;
        }
        for (int i = 0; i < count; i++) {
            node->data.conditional.statements[i] = statements[i];
        }
    } else {
        node->data.conditional.statements = NULL;
    }
    
    return node;
}

// Create binary operation node
ASTNode* ast_create_binary_op(BinaryOpType op, ASTNode *left, ASTNode *right) {
    ASTNode *node = malloc(sizeof(ASTNode));
    if (!node) return NULL;
    
    node->type = AST_BINARY_OP;
    node->data.binary_op.op = op;
    node->data.binary_op.left = left;
    node->data.binary_op.right = right;
    
    return node;
}

// Create identifier node
ASTNode* ast_create_identifier(const char *name) {
    ASTNode *node = malloc(sizeof(ASTNode));
    if (!node) return NULL;
    
    node->type = AST_IDENTIFIER;
    node->data.identifier.name = malloc(strlen(name) + 1);
    if (!node->data.identifier.name) {
        free(node);
        return NULL;
    }
    strcpy(node->data.identifier.name, name);
    
    return node;
}

// Create number node
ASTNode* ast_create_number(int value) {
    ASTNode *node = malloc(sizeof(ASTNode));
    if (!node) return NULL;
    
    node->type = AST_NUMBER;
    node->data.number.value = value;
    
    return node;
}

// Add statement to program
void ast_add_statement(ASTNode *program, ASTNode *statement) {
    if (!program || program->type != AST_PROGRAM || !statement) return;
    
    if (program->data.program.statement_count >= program->data.program.statement_capacity) {
        int new_capacity = program->data.program.statement_capacity == 0 ? 4 : 
                          program->data.program.statement_capacity * 2;
        ASTNode **new_statements = realloc(program->data.program.statements, 
                                          sizeof(ASTNode*) * new_capacity);
        if (!new_statements) return;
        
        program->data.program.statements = new_statements;
        program->data.program.statement_capacity = new_capacity;
    }
    
    program->data.program.statements[program->data.program.statement_count++] = statement;
}

// Destroy AST node and all children
void ast_destroy(ASTNode *node) {
    if (!node) return;
    
    switch (node->type) {
        case AST_PROGRAM:
            for (int i = 0; i < node->data.program.statement_count; i++) {
                ast_destroy(node->data.program.statements[i]);
            }
            free(node->data.program.statements);
            break;
            
        case AST_DECLARATION:
            free(node->data.declaration.identifier);
            break;
            
        case AST_ASSIGNMENT:
            free(node->data.assignment.identifier);
            ast_destroy(node->data.assignment.expression);
            break;
            
        case AST_CONDITIONAL:
            ast_destroy(node->data.conditional.condition);
            for (int i = 0; i < node->data.conditional.statement_count; i++) {
                ast_destroy(node->data.conditional.statements[i]);
            }
            free(node->data.conditional.statements);
            break;
            
        case AST_BINARY_OP:
            ast_destroy(node->data.binary_op.left);
            ast_destroy(node->data.binary_op.right);
            break;
            
        case AST_IDENTIFIER:
            free(node->data.identifier.name);
            break;
            
        case AST_NUMBER:
            // No dynamic memory to free
            break;
            
        default:
            break;
    }
    
    free(node);
}

// Print AST for debugging
void ast_print(ASTNode *node, int indent) {
    if (!node) return;
    
    for (int i = 0; i < indent; i++) printf("  ");
    
    switch (node->type) {
        case AST_PROGRAM:
            printf("Program\n");
            for (int i = 0; i < node->data.program.statement_count; i++) {
                ast_print(node->data.program.statements[i], indent + 1);
            }
            break;
            
        case AST_DECLARATION:
            printf("Declaration: %s\n", node->data.declaration.identifier);
            break;
            
        case AST_ASSIGNMENT:
            printf("Assignment: %s =\n", node->data.assignment.identifier);
            ast_print(node->data.assignment.expression, indent + 1);
            break;
            
        case AST_CONDITIONAL:
            printf("Conditional:\n");
            for (int i = 0; i < indent + 1; i++) printf("  ");
            printf("Condition:\n");
            ast_print(node->data.conditional.condition, indent + 2);
            for (int i = 0; i < indent + 1; i++) printf("  ");
            printf("Body:\n");
            for (int i = 0; i < node->data.conditional.statement_count; i++) {
                ast_print(node->data.conditional.statements[i], indent + 2);
            }
            break;
            
        case AST_BINARY_OP:
            printf("BinaryOp: %s\n", binary_op_to_string(node->data.binary_op.op));
            ast_print(node->data.binary_op.left, indent + 1);
            ast_print(node->data.binary_op.right, indent + 1);
            break;
            
        case AST_IDENTIFIER:
            printf("Identifier: %s\n", node->data.identifier.name);
            break;
            
        case AST_NUMBER:
            printf("Number: %d\n", node->data.number.value);
            break;
            
        default:
            printf("Unknown node type\n");
            break;
    }
}

// Convert binary operation to string
const char* binary_op_to_string(BinaryOpType op) {
    switch (op) {
        case OP_ADD: return "+";
        case OP_SUB: return "-";
        case OP_EQUAL: return "==";
        default: return "UNKNOWN";
    }
}
