#ifndef AST_H
#define AST_H

#include "lexer.h"

// AST Node types
typedef enum {
    AST_PROGRAM,
    AST_DECLARATION,
    AST_ASSIGNMENT,
    AST_CONDITIONAL,
    AST_EXPRESSION,
    AST_BINARY_OP,
    AST_IDENTIFIER,
    AST_NUMBER
} ASTNodeType;

// Binary operation types
typedef enum {
    OP_ADD,
    OP_SUB,
    OP_EQUAL
} BinaryOpType;

// Forward declaration
struct ASTNode;

// AST Node structure
typedef struct ASTNode {
    ASTNodeType type;
    union {
        // Program node
        struct {
            struct ASTNode **statements;
            int statement_count;
            int statement_capacity;
        } program;
        
        // Declaration node
        struct {
            char *identifier;
        } declaration;
        
        // Assignment node
        struct {
            char *identifier;
            struct ASTNode *expression;
        } assignment;
        
        // Conditional node
        struct {
            struct ASTNode *condition;
            struct ASTNode **statements;
            int statement_count;
            int statement_capacity;
        } conditional;
        
        // Binary operation node
        struct {
            BinaryOpType op;
            struct ASTNode *left;
            struct ASTNode *right;
        } binary_op;
        
        // Identifier node
        struct {
            char *name;
        } identifier;
        
        // Number node
        struct {
            int value;
        } number;
    } data;
} ASTNode;

// Function declarations
ASTNode* ast_create_program();
ASTNode* ast_create_declaration(const char *identifier);
ASTNode* ast_create_assignment(const char *identifier, ASTNode *expression);
ASTNode* ast_create_conditional(ASTNode *condition, ASTNode **statements, int count);
ASTNode* ast_create_binary_op(BinaryOpType op, ASTNode *left, ASTNode *right);
ASTNode* ast_create_identifier(const char *name);
ASTNode* ast_create_number(int value);

void ast_add_statement(ASTNode *program, ASTNode *statement);
void ast_destroy(ASTNode *node);
void ast_print(ASTNode *node, int indent);

const char* binary_op_to_string(BinaryOpType op);

#endif // AST_H
