#ifndef LEXER_H
#define LEXER_H

#include <stdio.h>

#define MAX_TOKEN_LEN 100

// Token types for SimpleLang
typedef enum {
    TOKEN_INT,          // "int" keyword
    TOKEN_IF,           // "if" keyword
    TOKEN_IDENTIFIER,   // variable names
    TOKEN_NUMBER,       // integer literals
    TOKEN_ASSIGN,       // "="
    TOKEN_PLUS,         // "+"
    TOKEN_MINUS,        // "-"
    TOKEN_EQUAL,        // "=="
    TOKEN_LPAREN,       // "("
    TOKEN_RPAREN,       // ")"
    TOKEN_LBRACE,       // "{"
    TOKEN_RBRACE,       // "}"
    TOKEN_SEMICOLON,    // ";"
    TOKEN_EOF,          // End of file
    TOKEN_UNKNOWN       // Unknown token
} TokenType;

// Token structure
typedef struct {
    TokenType type;
    char text[MAX_TOKEN_LEN];
    int line;
    int column;
} Token;

// Lexer structure
typedef struct {
    FILE *file;
    int current_char;
    int line;
    int column;
    int peek_char;
    int has_peek;
} Lexer;

// Function declarations
Lexer* lexer_create(FILE *file);
void lexer_destroy(Lexer *lexer);
Token lexer_next_token(Lexer *lexer);
const char* token_type_to_string(TokenType type);
void print_token(Token *token);

#endif // LEXER_H
