# SimpleLang Compiler Design Document

## Overview

This document describes the design and implementation of the SimpleLang compiler, a minimalistic high-level language compiler targeting an 8-bit CPU architecture.

## Architecture Overview

The compiler follows a traditional multi-pass design:

```
Source Code → Lexer → Parser → AST → Code Generator → Assembly Code
```

## Component Design

### 1. Lexical Analyzer (Lexer)

**Purpose**: Convert source code into tokens

**Implementation**: `lexer.h`, `lexer.c`

**Key Features**:
- Finite state machine for token recognition
- Support for keywords, identifiers, numbers, operators
- Comment handling (`//` style)
- Error reporting with line/column information

**Token Types**:
- `TOKEN_INT`: "int" keyword
- `TOKEN_IF`: "if" keyword  
- `TOKEN_IDENTIFIER`: Variable names
- `TOKEN_NUMBER`: Integer literals
- `TOKEN_ASSIGN`: "=" operator
- `TOKEN_PLUS`, `TOKEN_MINUS`: Arithmetic operators
- `TOKEN_EQUAL`: "==" comparison operator
- `TOKEN_LPAREN`, `TOKEN_RPAREN`: Parentheses
- `TOKEN_LBRACE`, `TOKEN_RBRACE`: Braces
- `TOKEN_SEMICOLON`: Statement terminator

### 2. Syntax Analyzer (Parser)

**Purpose**: Generate Abstract Syntax Tree from tokens

**Implementation**: `parser.h`, `parser.c`

**Parsing Strategy**: Recursive descent parser

**Grammar Rules**:
```
program → statement_list
statement_list → statement | statement statement_list
statement → declaration | assignment | conditional
declaration → "int" IDENTIFIER ";"
assignment → IDENTIFIER "=" expression ";"
conditional → "if" "(" condition ")" "{" statement_list "}"
condition → expression "==" expression
expression → term | term "+" term | term "-" term
term → IDENTIFIER | NUMBER
```

**Error Handling**:
- Syntax error detection with precise location
- Graceful error recovery
- Descriptive error messages

### 3. Abstract Syntax Tree (AST)

**Purpose**: Intermediate representation of the program structure

**Implementation**: `ast.h`, `ast.c`

**Node Types**:
- `AST_PROGRAM`: Root node containing all statements
- `AST_DECLARATION`: Variable declaration
- `AST_ASSIGNMENT`: Variable assignment
- `AST_CONDITIONAL`: If statement
- `AST_BINARY_OP`: Binary operations (+, -, ==)
- `AST_IDENTIFIER`: Variable reference
- `AST_NUMBER`: Integer literal

**Memory Management**:
- Dynamic allocation for flexible tree structure
- Recursive deallocation to prevent memory leaks
- Reference counting for shared nodes

### 4. Code Generator

**Purpose**: Translate AST to 8-bit CPU assembly code

**Implementation**: `codegen.h`, `codegen.c`

**Key Components**:
- Symbol table for variable management
- Label generation for control flow
- Register allocation strategy
- Assembly instruction emission

**Code Generation Strategy**:

#### Variable Management
- Variables stored in memory starting at address 200
- Symbol table tracks variable names and addresses
- Automatic memory allocation during declaration

#### Expression Evaluation
- Stack-based evaluation using CPU registers
- Register A: Primary accumulator
- Register B: Secondary operand storage
- Register C: Temporary storage for complex operations

#### Control Flow
- Labels generated for conditional jumps
- Zero flag used for equality comparisons
- Jump instructions for conditional execution

## 8-bit CPU Instruction Mapping

### Data Movement
| SimpleLang | Assembly | Description |
|------------|----------|-------------|
| `int a;` | `.data a = 0` | Variable declaration |
| `a = 10;` | `ldi A 10; sta %a` | Load immediate and store |
| `a = b;` | `lda %b; sta %a` | Load and store variable |

### Arithmetic Operations
| SimpleLang | Assembly | Description |
|------------|----------|-------------|
| `a + b` | `lda %a; mov B A; lda %b; add` | Addition |
| `a - b` | `lda %a; mov B A; lda %b; mov C A; mov A B; mov B C; sub` | Subtraction |

### Control Flow
| SimpleLang | Assembly | Description |
|------------|----------|-------------|
| `if (a == b)` | `lda %a; mov B A; lda %b; cmp; jz label` | Conditional jump |

## Error Handling Strategy

### Lexical Errors
- Invalid characters
- Malformed tokens
- Unterminated comments

### Syntax Errors
- Missing semicolons
- Unmatched braces/parentheses
- Invalid expression syntax
- Unexpected tokens

### Semantic Errors
- Undefined variables
- Type mismatches (future extension)
- Redeclared variables

## Testing Strategy

### Unit Tests
- Individual component testing
- Token recognition accuracy
- Parser correctness
- AST generation validation

### Integration Tests
- End-to-end compilation
- Assembly code verification
- Error handling validation

### Test Cases
1. **Basic Compilation**: Simple variable declaration and assignment
2. **Arithmetic Operations**: Addition and subtraction expressions
3. **Conditional Statements**: If statements with equality comparison
4. **Error Handling**: Syntax and semantic error detection
5. **Complex Expressions**: Multi-operator expressions

## Performance Considerations

### Memory Usage
- Efficient AST representation
- Minimal symbol table overhead
- Proper memory cleanup

### Compilation Speed
- Single-pass lexing
- Efficient recursive descent parsing
- Direct code generation without optimization passes

## Future Enhancements

### Language Features
- Loop constructs (for, while)
- Additional data types (char, boolean)
- Function definitions and calls
- Array support
- String literals

### Compiler Features
- Optimization passes
- Better error recovery
- Symbol table improvements
- Code generation optimizations

### Target Architecture
- Support for larger address spaces
- Multiple register architectures
- Different instruction sets

## Implementation Notes

### Register Usage Convention
- Register A: Primary accumulator for all operations
- Register B: Secondary operand in binary operations
- Register C: Temporary storage for complex operations
- Registers D-G: Available for future optimizations

### Memory Layout
- Code section: Program instructions
- Data section: Variable storage (starting at address 200)
- Stack: Not currently used (future enhancement)

### Assembly Format
- Compatible with 8-bit CPU assembler
- Uses symbolic references for variables (`%variable_name`)
- Includes comments for debugging
- Structured with `.text` and `.data` sections

## Conclusion

The SimpleLang compiler demonstrates fundamental compiler construction principles while targeting a specific 8-bit CPU architecture. The modular design allows for easy extension and modification, making it suitable for educational purposes and further development.
