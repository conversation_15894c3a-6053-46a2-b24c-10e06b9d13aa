#ifndef PARSER_H
#define PARSER_H

#include "lexer.h"
#include "ast.h"

// Parser structure
typedef struct {
    Lexer *lexer;
    Token current_token;
    int has_error;
    char error_message[256];
} Parser;

// Function declarations
Parser* parser_create(<PERSON><PERSON> *lexer);
void parser_destroy(Parser *parser);
ASTNode* parser_parse(Parser *parser);

// Internal parsing functions
ASTNode* parse_program(Parser *parser);
ASTNode* parse_statement(Parser *parser);
ASTNode* parse_declaration(Parser *parser);
ASTNode* parse_assignment(Parser *parser);
ASTNode* parse_conditional(Parser *parser);
ASTNode* parse_expression(Parser *parser);
ASTNode* parse_term(Parser *parser);
ASTNode* parse_condition(Parser *parser);

// Utility functions
void parser_advance(Parser *parser);
int parser_expect(Parser *parser, TokenType expected);
void parser_error(Parser *parser, const char *message);
int parser_has_error(Parser *parser);
const char* parser_get_error(Parser *parser);

#endif // PARSER_H
