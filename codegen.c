#include "codegen.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

// Create code generator
CodeGenerator* codegen_create(FILE *output) {
    CodeGenerator *codegen = malloc(sizeof(CodeGenerator));
    if (!codegen) return NULL;
    
    codegen->output = output;
    codegen->variable_count = 0;
    codegen->next_memory_address = 200; // Start variables at address 200
    codegen->label_count = 0;
    codegen->next_label_id = 0;
    codegen->has_error = 0;
    codegen->error_message[0] = '\0';
    
    return codegen;
}

// Destroy code generator
void codegen_destroy(CodeGenerator *codegen) {
    if (codegen) {
        free(codegen);
    }
}

// Main code generation entry point
int codegen_generate(CodeGenerator *codegen, ASTNode *ast) {
    if (!codegen || !ast) {
        codegen_error(codegen, "Invalid parameters");
        return 0;
    }
    
    emit_comment(codegen, "SimpleLang compiled program");
    emit_instruction(codegen, ".text");
    emit_instruction(codegen, "");
    
    int result = generate_program(codegen, ast);
    
    if (result) {
        emit_instruction(codegen, "");
        emit_instruction(codegen, "hlt");
        emit_instruction(codegen, "");
        emit_comment(codegen, "Data section");
        emit_instruction(codegen, ".data");
        emit_instruction(codegen, "");
        
        // Emit variable declarations
        for (int i = 0; i < codegen->variable_count; i++) {
            char data_line[256];
            snprintf(data_line, sizeof(data_line), "%s = 0", codegen->variables[i].name);
            emit_instruction(codegen, data_line);
        }
    }
    
    return result;
}

// Generate program
int generate_program(CodeGenerator *codegen, ASTNode *node) {
    if (node->type != AST_PROGRAM) {
        codegen_error(codegen, "Expected program node");
        return 0;
    }
    
    for (int i = 0; i < node->data.program.statement_count; i++) {
        if (!generate_statement(codegen, node->data.program.statements[i])) {
            return 0;
        }
    }
    
    return 1;
}

// Generate statement
int generate_statement(CodeGenerator *codegen, ASTNode *node) {
    switch (node->type) {
        case AST_DECLARATION:
            return generate_declaration(codegen, node);
        case AST_ASSIGNMENT:
            return generate_assignment(codegen, node);
        case AST_CONDITIONAL:
            return generate_conditional(codegen, node);
        default:
            codegen_error(codegen, "Unknown statement type");
            return 0;
    }
}

// Generate declaration
int generate_declaration(CodeGenerator *codegen, ASTNode *node) {
    if (node->type != AST_DECLARATION) {
        codegen_error(codegen, "Expected declaration node");
        return 0;
    }
    
    const char *var_name = node->data.declaration.identifier;
    
    // Check if variable already exists
    if (find_variable(codegen, var_name) >= 0) {
        char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "Variable '%s' already declared", var_name);
        codegen_error(codegen, error_msg);
        return 0;
    }
    
    // Add variable to symbol table
    if (add_variable(codegen, var_name) < 0) {
        codegen_error(codegen, "Failed to add variable");
        return 0;
    }
    
    char comment[256];
    snprintf(comment, sizeof(comment), "Declaration: %s", var_name);
    emit_comment(codegen, comment);
    
    return 1;
}

// Generate assignment
int generate_assignment(CodeGenerator *codegen, ASTNode *node) {
    if (node->type != AST_ASSIGNMENT) {
        codegen_error(codegen, "Expected assignment node");
        return 0;
    }
    
    const char *var_name = node->data.assignment.identifier;
    int var_addr = get_variable_address(codegen, var_name);
    
    if (var_addr < 0) {
        char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), "Undefined variable '%s'", var_name);
        codegen_error(codegen, error_msg);
        return 0;
    }
    
    char comment[256];
    snprintf(comment, sizeof(comment), "Assignment: %s =", var_name);
    emit_comment(codegen, comment);
    
    // Generate code for the expression (result will be in register A)
    if (!generate_expression(codegen, node->data.assignment.expression)) {
        return 0;
    }
    
    // Store result from A register to variable memory location
    char addr_str[16];
    snprintf(addr_str, sizeof(addr_str), "%%%s", var_name);
    emit_instruction_with_operand(codegen, "sta", addr_str);
    
    return 1;
}

// Generate expression
int generate_expression(CodeGenerator *codegen, ASTNode *node) {
    switch (node->type) {
        case AST_NUMBER: {
            // Load immediate value into A register
            char value_str[16];
            snprintf(value_str, sizeof(value_str), "%d", node->data.number.value);
            emit_instruction_with_operand(codegen, "ldi A", value_str);
            return 1;
        }

        case AST_IDENTIFIER: {
            // Load variable value into A register
            const char *var_name = node->data.identifier.name;
            int var_addr = get_variable_address(codegen, var_name);

            if (var_addr < 0) {
                char error_msg[256];
                snprintf(error_msg, sizeof(error_msg), "Undefined variable '%s'", var_name);
                codegen_error(codegen, error_msg);
                return 0;
            }

            char addr_str[32];
            snprintf(addr_str, sizeof(addr_str), "%%%s", var_name);
            emit_instruction_with_operand(codegen, "lda", addr_str);
            return 1;
        }

        case AST_BINARY_OP: {
            ASTNode *left = node->data.binary_op.left;
            ASTNode *right = node->data.binary_op.right;
            BinaryOpType op = node->data.binary_op.op;

            // Generate left operand (result in A)
            if (!generate_expression(codegen, left)) {
                return 0;
            }

            // Save A to temporary location (using register B for now)
            emit_instruction(codegen, "mov B A");

            // Generate right operand (result in A)
            if (!generate_expression(codegen, right)) {
                return 0;
            }

            // Perform operation
            switch (op) {
                case OP_ADD:
                    emit_comment(codegen, "Addition: B + A -> A");
                    emit_instruction(codegen, "add");
                    break;

                case OP_SUB:
                    emit_comment(codegen, "Subtraction: B - A -> A");
                    // For subtraction, we need A = B - A, but sub does A = A - B
                    // So we need to swap A and B first
                    emit_instruction(codegen, "mov C A");  // C = A (right operand)
                    emit_instruction(codegen, "mov A B");  // A = B (left operand)
                    emit_instruction(codegen, "mov B C");  // B = C (right operand)
                    emit_instruction(codegen, "sub");      // A = A - B = left - right
                    break;

                case OP_EQUAL:
                    emit_comment(codegen, "Equality comparison: B == A");
                    // Compare B with A, result affects zero flag
                    emit_instruction(codegen, "cmp");
                    // For conditional jumps, we'll use the zero flag
                    // The calling code will handle the jump logic
                    break;

                default:
                    codegen_error(codegen, "Unknown binary operation");
                    return 0;
            }

            return 1;
        }

        default:
            codegen_error(codegen, "Unknown expression type");
            return 0;
    }
}

// Add variable to symbol table
int add_variable(CodeGenerator *codegen, const char *name) {
    if (codegen->variable_count >= MAX_VARIABLES) {
        codegen_error(codegen, "Too many variables");
        return -1;
    }

    Variable *var = &codegen->variables[codegen->variable_count];
    strncpy(var->name, name, MAX_TOKEN_LEN - 1);
    var->name[MAX_TOKEN_LEN - 1] = '\0';
    var->memory_address = codegen->next_memory_address++;
    var->is_used = 1;

    return codegen->variable_count++;
}

// Find variable in symbol table
int find_variable(CodeGenerator *codegen, const char *name) {
    for (int i = 0; i < codegen->variable_count; i++) {
        if (strcmp(codegen->variables[i].name, name) == 0) {
            return i;
        }
    }
    return -1;
}

// Get variable memory address
int get_variable_address(CodeGenerator *codegen, const char *name) {
    int index = find_variable(codegen, name);
    if (index >= 0) {
        return codegen->variables[index].memory_address;
    }
    return -1;
}

// Create new label
int create_label(CodeGenerator *codegen, char *label_name) {
    if (codegen->label_count >= MAX_LABELS) {
        codegen_error(codegen, "Too many labels");
        return -1;
    }

    int label_id = codegen->next_label_id++;
    snprintf(label_name, 32, "end_%d", label_id);

    Label *label = &codegen->labels[codegen->label_count++];
    strncpy(label->name, label_name, MAX_TOKEN_LEN - 1);
    label->name[MAX_TOKEN_LEN - 1] = '\0';
    label->address = -1; // Will be set when label is emitted

    return label_id;
}

// Emit label
void emit_label(CodeGenerator *codegen, const char *label_name) {
    fprintf(codegen->output, "%s:\n", label_name);
}

// Emit instruction
void emit_instruction(CodeGenerator *codegen, const char *instruction) {
    if (strlen(instruction) == 0) {
        fprintf(codegen->output, "\n");
    } else {
        fprintf(codegen->output, "\t%s\n", instruction);
    }
}

// Emit instruction with operand
void emit_instruction_with_operand(CodeGenerator *codegen, const char *instruction, const char *operand) {
    fprintf(codegen->output, "\t%s %s\n", instruction, operand);
}

// Emit instruction with address
void emit_instruction_with_address(CodeGenerator *codegen, const char *instruction, int address) {
    fprintf(codegen->output, "\t%s %d\n", instruction, address);
}

// Emit comment
void emit_comment(CodeGenerator *codegen, const char *comment) {
    fprintf(codegen->output, "\t// %s\n", comment);
}

// Set error
void codegen_error(CodeGenerator *codegen, const char *message) {
    if (codegen) {
        codegen->has_error = 1;
        strncpy(codegen->error_message, message, sizeof(codegen->error_message) - 1);
        codegen->error_message[sizeof(codegen->error_message) - 1] = '\0';
    }
}

// Check if has error
int codegen_has_error(CodeGenerator *codegen) {
    return codegen ? codegen->has_error : 1;
}

// Get error message
const char* codegen_get_error(CodeGenerator *codegen) {
    return codegen ? codegen->error_message : "Invalid code generator";
}

// Generate conditional
int generate_conditional(CodeGenerator *codegen, ASTNode *node) {
    if (node->type != AST_CONDITIONAL) {
        codegen_error(codegen, "Expected conditional node");
        return 0;
    }
    
    emit_comment(codegen, "Conditional statement");
    
    // Generate condition evaluation
    if (!generate_expression(codegen, node->data.conditional.condition)) {
        return 0;
    }
    
    // Create labels for conditional jump
    char end_label[32];
    int label_id = create_label(codegen, end_label);
    if (label_id < 0) {
        return 0;
    }
    
    // Jump to end if condition is false (zero flag not set)
    char label_ref[32];
    snprintf(label_ref, sizeof(label_ref), "%%end_%d", label_id);
    emit_instruction_with_operand(codegen, "jz", label_ref);
    
    // Generate statements inside conditional block
    for (int i = 0; i < node->data.conditional.statement_count; i++) {
        if (!generate_statement(codegen, node->data.conditional.statements[i])) {
            return 0;
        }
    }
    
    // Emit end label
    emit_label(codegen, end_label);
    
    return 1;
}
