#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/wait.h>

// Test result structure
typedef struct {
    char *name;
    int passed;
    char *error_message;
} TestResult;

// Test cases
TestResult tests[10];
int test_count = 0;

void add_test_result(const char *name, int passed, const char *error) {
    tests[test_count].name = malloc(strlen(name) + 1);
    strcpy(tests[test_count].name, name);
    tests[test_count].passed = passed;
    
    if (error) {
        tests[test_count].error_message = malloc(strlen(error) + 1);
        strcpy(tests[test_count].error_message, error);
    } else {
        tests[test_count].error_message = NULL;
    }
    
    test_count++;
}

int run_compiler(const char *input_file, const char *output_file) {
    char command[512];
    snprintf(command, sizeof(command), "./simplelang %s %s", input_file, output_file);
    
    int result = system(command);
    return WEXITSTATUS(result) == 0;
}

void create_test_file(const char *filename, const char *content) {
    FILE *file = fopen(filename, "w");
    if (file) {
        fprintf(file, "%s", content);
        fclose(file);
    }
}

void test_basic_compilation() {
    const char *program = 
        "int a;\n"
        "a = 10;\n";
    
    create_test_file("test_basic.sl", program);
    
    if (run_compiler("test_basic.sl", "test_basic.asm")) {
        // Check if output file exists
        FILE *output = fopen("test_basic.asm", "r");
        if (output) {
            fclose(output);
            add_test_result("Basic Compilation", 1, NULL);
        } else {
            add_test_result("Basic Compilation", 0, "Output file not created");
        }
    } else {
        add_test_result("Basic Compilation", 0, "Compiler failed");
    }
    
    unlink("test_basic.sl");
    unlink("test_basic.asm");
}

void test_arithmetic_operations() {
    const char *program = 
        "int a;\n"
        "int b;\n"
        "int c;\n"
        "a = 10;\n"
        "b = 5;\n"
        "c = a + b;\n"
        "c = a - b;\n";
    
    create_test_file("test_arithmetic.sl", program);
    
    if (run_compiler("test_arithmetic.sl", "test_arithmetic.asm")) {
        add_test_result("Arithmetic Operations", 1, NULL);
    } else {
        add_test_result("Arithmetic Operations", 0, "Compilation failed");
    }
    
    unlink("test_arithmetic.sl");
    unlink("test_arithmetic.asm");
}

void test_conditional_statements() {
    const char *program = 
        "int a;\n"
        "int b;\n"
        "a = 10;\n"
        "b = 10;\n"
        "if (a == b) {\n"
        "    a = a + 1;\n"
        "}\n";
    
    create_test_file("test_conditional.sl", program);
    
    if (run_compiler("test_conditional.sl", "test_conditional.asm")) {
        add_test_result("Conditional Statements", 1, NULL);
    } else {
        add_test_result("Conditional Statements", 0, "Compilation failed");
    }
    
    unlink("test_conditional.sl");
    unlink("test_conditional.asm");
}

void test_syntax_error_handling() {
    const char *program = 
        "int a\n"  // Missing semicolon
        "a = 10;\n";
    
    create_test_file("test_syntax_error.sl", program);
    
    // This should fail
    if (!run_compiler("test_syntax_error.sl", "test_syntax_error.asm")) {
        add_test_result("Syntax Error Handling", 1, NULL);
    } else {
        add_test_result("Syntax Error Handling", 0, "Should have failed but didn't");
    }
    
    unlink("test_syntax_error.sl");
    unlink("test_syntax_error.asm");
}

void test_undefined_variable() {
    const char *program = 
        "int a;\n"
        "b = 10;\n";  // b is not declared
    
    create_test_file("test_undefined.sl", program);
    
    // This should fail
    if (!run_compiler("test_undefined.sl", "test_undefined.asm")) {
        add_test_result("Undefined Variable Detection", 1, NULL);
    } else {
        add_test_result("Undefined Variable Detection", 0, "Should have failed but didn't");
    }
    
    unlink("test_undefined.sl");
    unlink("test_undefined.asm");
}

void test_complex_expression() {
    const char *program = 
        "int a;\n"
        "int b;\n"
        "int c;\n"
        "int result;\n"
        "a = 10;\n"
        "b = 5;\n"
        "c = 3;\n"
        "result = a + b - c;\n";
    
    create_test_file("test_complex.sl", program);
    
    if (run_compiler("test_complex.sl", "test_complex.asm")) {
        add_test_result("Complex Expressions", 1, NULL);
    } else {
        add_test_result("Complex Expressions", 0, "Compilation failed");
    }
    
    unlink("test_complex.sl");
    unlink("test_complex.asm");
}

void run_all_tests() {
    printf("SimpleLang Compiler Test Suite\n");
    printf("==============================\n\n");
    
    // Check if compiler exists
    if (access("./simplelang", F_OK) != 0) {
        printf("Error: Compiler executable './simplelang' not found.\n");
        printf("Please run 'make' to build the compiler first.\n");
        return;
    }
    
    test_basic_compilation();
    test_arithmetic_operations();
    test_conditional_statements();
    test_syntax_error_handling();
    test_undefined_variable();
    test_complex_expression();
    
    // Print results
    int passed = 0;
    int total = test_count;
    
    printf("Test Results:\n");
    printf("-------------\n");
    
    for (int i = 0; i < test_count; i++) {
        printf("%-30s: %s", tests[i].name, tests[i].passed ? "PASS" : "FAIL");
        if (!tests[i].passed && tests[i].error_message) {
            printf(" (%s)", tests[i].error_message);
        }
        printf("\n");
        
        if (tests[i].passed) passed++;
    }
    
    printf("\n");
    printf("Summary: %d/%d tests passed (%.1f%%)\n", 
           passed, total, (float)passed / total * 100.0);
    
    if (passed == total) {
        printf("All tests passed! ✓\n");
    } else {
        printf("Some tests failed. ✗\n");
    }
    
    // Cleanup
    for (int i = 0; i < test_count; i++) {
        free(tests[i].name);
        if (tests[i].error_message) {
            free(tests[i].error_message);
        }
    }
}

int main() {
    run_all_tests();
    return 0;
}
