CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g
TARGET = simplelang
SOURCES = compiler.c lexer.c parser.c ast.c codegen.c
OBJECTS = $(SOURCES:.c=.o)
HEADERS = lexer.h parser.h ast.h codegen.h

# Default target
all: $(TARGET)

# Build the compiler
$(TARGET): $(OBJECTS)
	$(CC) $(CFLAGS) -o $(TARGET) $(OBJECTS)

# Compile source files
%.o: %.c $(HEADERS)
	$(CC) $(CFLAGS) -c $< -o $@

# Clean build artifacts
clean:
	rm -f $(OBJECTS) $(TARGET) *.asm test_suite

# Test the compiler with example programs
test: $(TARGET)
	@echo "Testing SimpleLang compiler..."
	@echo "Creating test program..."
	@echo 'int a;' > test.sl
	@echo 'int b;' >> test.sl
	@echo 'int c;' >> test.sl
	@echo 'a = 10;' >> test.sl
	@echo 'b = 20;' >> test.sl
	@echo 'c = a + b;' >> test.sl
	@echo 'if (c == 30) {' >> test.sl
	@echo '    c = c + 1;' >> test.sl
	@echo '}' >> test.sl
	@echo "Compiling test program..."
	./$(TARGET) test.sl test.asm
	@echo "Generated assembly:"
	@cat test.asm

# Run comprehensive test suite
test-suite: $(TARGET)
	@echo "Building test suite..."
	$(CC) $(CFLAGS) -o test_suite test_suite.c
	@echo "Running test suite..."
	./test_suite
	@rm -f test_suite

# Test with the 8-bit CPU simulator (if available)
test-cpu: test
	@if [ -f "8bit-computer/asm/asm.py" ]; then \
		echo "Converting to CPU format..."; \
		python3 8bit-computer/asm/asm.py test.asm > memory.list; \
		echo "Memory list generated."; \
	else \
		echo "8-bit CPU simulator not found. Please ensure 8bit-computer is available."; \
	fi

# Install dependencies (for development)
install-deps:
	@echo "Installing development dependencies..."
	@which gcc > /dev/null || (echo "Please install gcc" && exit 1)
	@which make > /dev/null || (echo "Please install make" && exit 1)
	@echo "Dependencies satisfied."

# Debug build
debug: CFLAGS += -DDEBUG -O0
debug: $(TARGET)

# Release build
release: CFLAGS += -O2 -DNDEBUG
release: clean $(TARGET)

# Run static analysis
analyze:
	@which cppcheck > /dev/null && cppcheck --enable=all --std=c99 $(SOURCES) || echo "cppcheck not available"

# Format code
format:
	@which clang-format > /dev/null && clang-format -i $(SOURCES) $(HEADERS) || echo "clang-format not available"

# Show help
help:
	@echo "SimpleLang Compiler Makefile"
	@echo ""
	@echo "Targets:"
	@echo "  all          - Build the compiler (default)"
	@echo "  clean        - Remove build artifacts"
	@echo "  test         - Build and test with example program"
	@echo "  test-suite   - Run comprehensive test suite"
	@echo "  test-cpu     - Test with 8-bit CPU simulator"
	@echo "  debug        - Build debug version"
	@echo "  release      - Build optimized release version"
	@echo "  analyze      - Run static analysis"
	@echo "  format       - Format source code"
	@echo "  install-deps - Check/install dependencies"
	@echo "  help         - Show this help"

.PHONY: all clean test test-cpu install-deps debug release analyze format help
