#include "lexer.h"
#include <stdlib.h>
#include <string.h>
#include <ctype.h>

// Create a new lexer
Lexer* lexer_create(FILE *file) {
    Lexer *lexer = malloc(sizeof(Lexer));
    if (!lexer) return NULL;
    
    lexer->file = file;
    lexer->line = 1;
    lexer->column = 0;
    lexer->has_peek = 0;
    lexer->current_char = fgetc(file);
    if (lexer->current_char != EOF) {
        lexer->column = 1;
    }
    
    return lexer;
}

// Destroy lexer
void lexer_destroy(Lex<PERSON> *lexer) {
    if (lexer) {
        free(lexer);
    }
}

// Advance to next character
static void advance(Lex<PERSON> *lexer) {
    if (lexer->has_peek) {
        lexer->current_char = lexer->peek_char;
        lexer->has_peek = 0;
    } else {
        lexer->current_char = fgetc(lexer->file);
    }
    
    if (lexer->current_char == '\n') {
        lexer->line++;
        lexer->column = 0;
    } else if (lexer->current_char != EOF) {
        lexer->column++;
    }
}

// Peek at next character without consuming it
static int peek(Lexer *lexer) {
    if (!lexer->has_peek) {
        lexer->peek_char = fgetc(lexer->file);
        lexer->has_peek = 1;
    }
    return lexer->peek_char;
}

// Skip whitespace and comments
static void skip_whitespace(Lexer *lexer) {
    while (lexer->current_char != EOF && isspace(lexer->current_char)) {
        advance(lexer);
    }
    
    // Skip single-line comments starting with //
    if (lexer->current_char == '/' && peek(lexer) == '/') {
        while (lexer->current_char != EOF && lexer->current_char != '\n') {
            advance(lexer);
        }
        skip_whitespace(lexer); // Recursively skip more whitespace
    }
}

// Read identifier or keyword
static Token read_identifier(Lexer *lexer) {
    Token token;
    int len = 0;
    
    token.line = lexer->line;
    token.column = lexer->column;
    
    while (lexer->current_char != EOF && 
           (isalnum(lexer->current_char) || lexer->current_char == '_') &&
           len < MAX_TOKEN_LEN - 1) {
        token.text[len++] = lexer->current_char;
        advance(lexer);
    }
    
    token.text[len] = '\0';
    
    // Check for keywords
    if (strcmp(token.text, "int") == 0) {
        token.type = TOKEN_INT;
    } else if (strcmp(token.text, "if") == 0) {
        token.type = TOKEN_IF;
    } else {
        token.type = TOKEN_IDENTIFIER;
    }
    
    return token;
}

// Read number
static Token read_number(Lexer *lexer) {
    Token token;
    int len = 0;
    
    token.line = lexer->line;
    token.column = lexer->column;
    token.type = TOKEN_NUMBER;
    
    while (lexer->current_char != EOF && isdigit(lexer->current_char) &&
           len < MAX_TOKEN_LEN - 1) {
        token.text[len++] = lexer->current_char;
        advance(lexer);
    }
    
    token.text[len] = '\0';
    return token;
}

// Get next token
Token lexer_next_token(Lexer *lexer) {
    Token token;
    
    skip_whitespace(lexer);
    
    token.line = lexer->line;
    token.column = lexer->column;
    
    if (lexer->current_char == EOF) {
        token.type = TOKEN_EOF;
        token.text[0] = '\0';
        return token;
    }
    
    // Handle identifiers and keywords
    if (isalpha(lexer->current_char) || lexer->current_char == '_') {
        return read_identifier(lexer);
    }
    
    // Handle numbers
    if (isdigit(lexer->current_char)) {
        return read_number(lexer);
    }
    
    // Handle operators and punctuation
    switch (lexer->current_char) {
        case '=':
            if (peek(lexer) == '=') {
                token.type = TOKEN_EQUAL;
                strcpy(token.text, "==");
                advance(lexer); // consume first '='
                advance(lexer); // consume second '='
            } else {
                token.type = TOKEN_ASSIGN;
                strcpy(token.text, "=");
                advance(lexer);
            }
            break;
        case '+':
            token.type = TOKEN_PLUS;
            strcpy(token.text, "+");
            advance(lexer);
            break;
        case '-':
            token.type = TOKEN_MINUS;
            strcpy(token.text, "-");
            advance(lexer);
            break;
        case '(':
            token.type = TOKEN_LPAREN;
            strcpy(token.text, "(");
            advance(lexer);
            break;
        case ')':
            token.type = TOKEN_RPAREN;
            strcpy(token.text, ")");
            advance(lexer);
            break;
        case '{':
            token.type = TOKEN_LBRACE;
            strcpy(token.text, "{");
            advance(lexer);
            break;
        case '}':
            token.type = TOKEN_RBRACE;
            strcpy(token.text, "}");
            advance(lexer);
            break;
        case ';':
            token.type = TOKEN_SEMICOLON;
            strcpy(token.text, ";");
            advance(lexer);
            break;
        default:
            token.type = TOKEN_UNKNOWN;
            token.text[0] = lexer->current_char;
            token.text[1] = '\0';
            advance(lexer);
            break;
    }
    
    return token;
}

// Convert token type to string for debugging
const char* token_type_to_string(TokenType type) {
    switch (type) {
        case TOKEN_INT: return "INT";
        case TOKEN_IF: return "IF";
        case TOKEN_IDENTIFIER: return "IDENTIFIER";
        case TOKEN_NUMBER: return "NUMBER";
        case TOKEN_ASSIGN: return "ASSIGN";
        case TOKEN_PLUS: return "PLUS";
        case TOKEN_MINUS: return "MINUS";
        case TOKEN_EQUAL: return "EQUAL";
        case TOKEN_LPAREN: return "LPAREN";
        case TOKEN_RPAREN: return "RPAREN";
        case TOKEN_LBRACE: return "LBRACE";
        case TOKEN_RBRACE: return "RBRACE";
        case TOKEN_SEMICOLON: return "SEMICOLON";
        case TOKEN_EOF: return "EOF";
        case TOKEN_UNKNOWN: return "UNKNOWN";
        default: return "INVALID";
    }
}

// Print token for debugging
void print_token(Token *token) {
    printf("Token: %s, Text: '%s', Line: %d, Column: %d\n",
           token_type_to_string(token->type), token->text, token->line, token->column);
}
