01101000 OR
10000111 MOV A M
00101000 POP A
10000100 MOV A E
10000101 MOV A F
01001000 SUB
01100000 AND
10111011 MOV M D
10111010 MOV M C
10001110 MOV B G
10001111 MOV B M
10011010 MOV D C
10011011 MOV invalid
10101100 MOV F E
10101101 MOV invalid
10111100 MOV M E
00101001 POP B
10000110 MOV A G
00010101 LDI F
00010100 LDI E
10111101 MOV M F
10011001 MOV D B
10011000 MOV D A
00000110 CMP
10001101 MOV B F
10001100 MOV B E
10100110 MOV E G
10100111 MOV E M
10101111 MOV F M
10101110 MOV F G
00101010 POP C
00101011 POP D
10110010 MOV G C
10110011 MOV G D
10010110 MOV C G
10010111 MOV C M
00000100 IN
00000101 HLT
00010110 LDI G
00011100 JNC
10000010 MOV A C
10000011 MOV A D
00011010 JNZ
00011011 JC
10100101 MOV E F
10100100 MOV invalid
10011111 MOV D M
01000000 ADD
10010101 MOV C F
10010100 MOV C E
10110001 MOV G B
10110000 MOV G A
01011000 DEC
10000001 MOV A B
10000000 MOV invalid
10011100 MOV D E
10011101 MOV D F
10001000 MOV B A
10001001 MOV invalid
00100011 PUSH D
00100010 PUSH C
10111110 MOV M G
00100000 PUSH A
00100001 PUSH B
00000001 CALL
00000000 NOP
00011001 JZ
10011110 MOV D G
10001011 MOV B D
10001010 MOV B C
10010000 MOV C A
10010001 MOV C B
10110100 MOV G E
10110101 MOV G F
10100000 MOV E A
10100001 MOV E B
01111000 ADC
00000010 RET
00000011 OUT
10101010 MOV F C
10101011 MOV F D
00010011 LDI D
00010010 LDI C
00101110 POP G
10010011 MOV C D
10010010 MOV invalid
10110111 MOV G M
10110110 MOV invalid
10100011 MOV E D
10100010 MOV E C
10101001 MOV F B
10101000 MOV F A
00100101 PUSH F
00100100 PUSH E
00010000 LDI A
00010001 LDI B
00011000 JMP
00101100 POP E
00101101 POP F
10111000 MOV M A
10111001 MOV M B
01110000 XOR
01010000 INC
00100110 PUSH G
