#include "parser.h"
#include <stdlib.h>
#include <string.h>
#include <stdio.h>

// Create parser
Parser* parser_create(Lexer *lexer) {
    Parser *parser = malloc(sizeof(Parser));
    if (!parser) return NULL;
    
    parser->lexer = lexer;
    parser->has_error = 0;
    parser->error_message[0] = '\0';
    parser->current_token = lexer_next_token(lexer);
    
    return parser;
}

// Destroy parser
void parser_destroy(Parser *parser) {
    if (parser) {
        free(parser);
    }
}

// Advance to next token
void parser_advance(Parser *parser) {
    parser->current_token = lexer_next_token(parser->lexer);
}

// Expect specific token type
int parser_expect(Parser *parser, TokenType expected) {
    if (parser->current_token.type == expected) {
        parser_advance(parser);
        return 1;
    } else {
        char error_msg[256];
        snprintf(error_msg, sizeof(error_msg), 
                "Expected %s but got %s at line %d, column %d",
                token_type_to_string(expected),
                token_type_to_string(parser->current_token.type),
                parser->current_token.line,
                parser->current_token.column);
        parser_error(parser, error_msg);
        return 0;
    }
}

// Set parser error
void parser_error(Parser *parser, const char *message) {
    parser->has_error = 1;
    strncpy(parser->error_message, message, sizeof(parser->error_message) - 1);
    parser->error_message[sizeof(parser->error_message) - 1] = '\0';
}

// Check if parser has error
int parser_has_error(Parser *parser) {
    return parser->has_error;
}

// Get parser error message
const char* parser_get_error(Parser *parser) {
    return parser->error_message;
}

// Parse main entry point
ASTNode* parser_parse(Parser *parser) {
    return parse_program(parser);
}

// Parse program (list of statements)
ASTNode* parse_program(Parser *parser) {
    ASTNode *program = ast_create_program();
    if (!program) {
        parser_error(parser, "Failed to create program node");
        return NULL;
    }
    
    while (parser->current_token.type != TOKEN_EOF && !parser->has_error) {
        ASTNode *statement = parse_statement(parser);
        if (statement) {
            ast_add_statement(program, statement);
        } else if (!parser->has_error) {
            parser_error(parser, "Failed to parse statement");
            break;
        }
    }
    
    if (parser->has_error) {
        ast_destroy(program);
        return NULL;
    }
    
    return program;
}

// Parse statement
ASTNode* parse_statement(Parser *parser) {
    switch (parser->current_token.type) {
        case TOKEN_INT:
            return parse_declaration(parser);
        case TOKEN_IDENTIFIER:
            return parse_assignment(parser);
        case TOKEN_IF:
            return parse_conditional(parser);
        default:
            parser_error(parser, "Unexpected token in statement");
            return NULL;
    }
}

// Parse declaration: int identifier;
ASTNode* parse_declaration(Parser *parser) {
    if (!parser_expect(parser, TOKEN_INT)) {
        return NULL;
    }
    
    if (parser->current_token.type != TOKEN_IDENTIFIER) {
        parser_error(parser, "Expected identifier after 'int'");
        return NULL;
    }
    
    char identifier[MAX_TOKEN_LEN];
    strcpy(identifier, parser->current_token.text);
    parser_advance(parser);
    
    if (!parser_expect(parser, TOKEN_SEMICOLON)) {
        return NULL;
    }
    
    return ast_create_declaration(identifier);
}

// Parse assignment: identifier = expression;
ASTNode* parse_assignment(Parser *parser) {
    if (parser->current_token.type != TOKEN_IDENTIFIER) {
        parser_error(parser, "Expected identifier in assignment");
        return NULL;
    }
    
    char identifier[MAX_TOKEN_LEN];
    strcpy(identifier, parser->current_token.text);
    parser_advance(parser);
    
    if (!parser_expect(parser, TOKEN_ASSIGN)) {
        return NULL;
    }
    
    ASTNode *expression = parse_expression(parser);
    if (!expression) {
        return NULL;
    }
    
    if (!parser_expect(parser, TOKEN_SEMICOLON)) {
        ast_destroy(expression);
        return NULL;
    }
    
    return ast_create_assignment(identifier, expression);
}

// Parse conditional: if (condition) { statements }
ASTNode* parse_conditional(Parser *parser) {
    if (!parser_expect(parser, TOKEN_IF)) {
        return NULL;
    }

    if (!parser_expect(parser, TOKEN_LPAREN)) {
        return NULL;
    }

    ASTNode *condition = parse_condition(parser);
    if (!condition) {
        return NULL;
    }

    if (!parser_expect(parser, TOKEN_RPAREN)) {
        ast_destroy(condition);
        return NULL;
    }

    if (!parser_expect(parser, TOKEN_LBRACE)) {
        ast_destroy(condition);
        return NULL;
    }

    // Parse statements inside the conditional block
    ASTNode **statements = NULL;
    int statement_count = 0;
    int statement_capacity = 0;

    while (parser->current_token.type != TOKEN_RBRACE &&
           parser->current_token.type != TOKEN_EOF &&
           !parser->has_error) {

        ASTNode *statement = parse_statement(parser);
        if (!statement) {
            // Clean up allocated statements
            for (int i = 0; i < statement_count; i++) {
                ast_destroy(statements[i]);
            }
            free(statements);
            ast_destroy(condition);
            return NULL;
        }

        // Resize array if needed
        if (statement_count >= statement_capacity) {
            statement_capacity = statement_capacity == 0 ? 4 : statement_capacity * 2;
            ASTNode **new_statements = realloc(statements, sizeof(ASTNode*) * statement_capacity);
            if (!new_statements) {
                ast_destroy(statement);
                for (int i = 0; i < statement_count; i++) {
                    ast_destroy(statements[i]);
                }
                free(statements);
                ast_destroy(condition);
                parser_error(parser, "Memory allocation failed");
                return NULL;
            }
            statements = new_statements;
        }

        statements[statement_count++] = statement;
    }

    if (!parser_expect(parser, TOKEN_RBRACE)) {
        for (int i = 0; i < statement_count; i++) {
            ast_destroy(statements[i]);
        }
        free(statements);
        ast_destroy(condition);
        return NULL;
    }

    ASTNode *conditional = ast_create_conditional(condition, statements, statement_count);
    free(statements); // ast_create_conditional copies the array

    return conditional;
}

// Parse condition (only equality for now)
ASTNode* parse_condition(Parser *parser) {
    ASTNode *left = parse_expression(parser);
    if (!left) {
        return NULL;
    }

    if (parser->current_token.type != TOKEN_EQUAL) {
        parser_error(parser, "Expected '==' in condition");
        ast_destroy(left);
        return NULL;
    }

    parser_advance(parser); // consume '=='

    ASTNode *right = parse_expression(parser);
    if (!right) {
        ast_destroy(left);
        return NULL;
    }

    return ast_create_binary_op(OP_EQUAL, left, right);
}

// Parse expression (handles + and -)
ASTNode* parse_expression(Parser *parser) {
    ASTNode *left = parse_term(parser);
    if (!left) {
        return NULL;
    }

    while (parser->current_token.type == TOKEN_PLUS ||
           parser->current_token.type == TOKEN_MINUS) {

        BinaryOpType op = (parser->current_token.type == TOKEN_PLUS) ? OP_ADD : OP_SUB;
        parser_advance(parser);

        ASTNode *right = parse_term(parser);
        if (!right) {
            ast_destroy(left);
            return NULL;
        }

        left = ast_create_binary_op(op, left, right);
        if (!left) {
            ast_destroy(right);
            parser_error(parser, "Failed to create binary operation node");
            return NULL;
        }
    }

    return left;
}

// Parse term (identifier or number)
ASTNode* parse_term(Parser *parser) {
    switch (parser->current_token.type) {
        case TOKEN_IDENTIFIER: {
            char name[MAX_TOKEN_LEN];
            strcpy(name, parser->current_token.text);
            parser_advance(parser);
            return ast_create_identifier(name);
        }

        case TOKEN_NUMBER: {
            int value = atoi(parser->current_token.text);
            parser_advance(parser);
            return ast_create_number(value);
        }

        default:
            parser_error(parser, "Expected identifier or number");
            return NULL;
    }
}
