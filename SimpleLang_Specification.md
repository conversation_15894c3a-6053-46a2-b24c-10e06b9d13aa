# SimpleLang Language Specification

## Overview
SimpleLang is a minimalistic high-level language designed for an 8-bit CPU. It supports basic programming constructs without loops, making it suitable for educational purposes and simple computational tasks.

## Language Constructs

### 1. Variable Declaration
**Syntax:** `int <identifier>;`
**Description:** Declares an integer variable
**Example:**
```
int a;
int counter;
int result;
```

### 2. Assignment
**Syntax:** `<identifier> = <expression>;`
**Description:** Assigns a value or expression result to a variable
**Examples:**
```
a = 10;
b = 20;
c = a + b;
result = x - y;
```

### 3. Arithmetic Operations
**Supported Operators:** `+` (addition), `-` (subtraction)
**Syntax:** `<operand> <operator> <operand>`
**Examples:**
```
sum = a + b;
difference = x - y;
result = 15 + 25;
```

### 4. Conditional Statements
**Syntax:** `if (<condition>) { <statements> }`
**Supported Comparison:** `==` (equality)
**Examples:**
```
if (a == b) {
    result = a + 1;
}

if (sum == 30) {
    sum = sum + 1;
}
```

## Grammar (BNF)
```
<program> ::= <statement_list>
<statement_list> ::= <statement> | <statement> <statement_list>
<statement> ::= <declaration> | <assignment> | <conditional>
<declaration> ::= "int" <identifier> ";"
<assignment> ::= <identifier> "=" <expression> ";"
<conditional> ::= "if" "(" <condition> ")" "{" <statement_list> "}"
<condition> ::= <expression> "==" <expression>
<expression> ::= <term> | <term> "+" <term> | <term> "-" <term>
<term> ::= <identifier> | <number>
<identifier> ::= [a-zA-Z][a-zA-Z0-9]*
<number> ::= [0-9]+
```

## Example Program
```c
// Variable declarations
int a;
int b;
int c;

// Assignments
a = 10;
b = 20;
c = a + b;

// Conditional
if (c == 30) {
    c = c + 1;
}
```

## Target Architecture Mapping
- Variables are stored in memory locations
- Arithmetic operations map to ALU instructions (add, sub)
- Conditionals use comparison (cmp) and conditional jumps (jz, jnz)
- Memory operations use load/store instructions (ldi, mov)

## Limitations
- No loops (for, while)
- Only integer data type
- Limited arithmetic operations (+ and -)
- Single comparison operator (==)
- No function definitions
- No arrays or complex data structures
