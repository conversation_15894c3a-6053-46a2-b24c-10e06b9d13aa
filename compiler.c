#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "lexer.h"
#include "parser.h"
#include "codegen.h"

void print_usage(const char *program_name) {
    printf("Usage: %s <input_file> [output_file]\n", program_name);
    printf("  input_file:  SimpleLang source file\n");
    printf("  output_file: Assembly output file (default: output.asm)\n");
}

int main(int argc, char *argv[]) {
    if (argc < 2 || argc > 3) {
        print_usage(argv[0]);
        return 1;
    }
    
    const char *input_filename = argv[1];
    const char *output_filename = (argc == 3) ? argv[2] : "output.asm";
    
    // Open input file
    FILE *input_file = fopen(input_filename, "r");
    if (!input_file) {
        fprintf(stderr, "Error: Cannot open input file '%s'\n", input_filename);
        return 1;
    }
    
    // Open output file
    FILE *output_file = fopen(output_filename, "w");
    if (!output_file) {
        fprintf(stderr, "Error: Cannot create output file '%s'\n", output_filename);
        fclose(input_file);
        return 1;
    }
    
    printf("SimpleLang Compiler\n");
    printf("Input:  %s\n", input_filename);
    printf("Output: %s\n", output_filename);
    printf("\n");
    
    // Create lexer
    Lexer *lexer = lexer_create(input_file);
    if (!lexer) {
        fprintf(stderr, "Error: Failed to create lexer\n");
        fclose(input_file);
        fclose(output_file);
        return 1;
    }
    
    // Create parser
    Parser *parser = parser_create(lexer);
    if (!parser) {
        fprintf(stderr, "Error: Failed to create parser\n");
        lexer_destroy(lexer);
        fclose(input_file);
        fclose(output_file);
        return 1;
    }
    
    printf("Parsing...\n");
    
    // Parse the input
    ASTNode *ast = parser_parse(parser);
    if (!ast || parser_has_error(parser)) {
        fprintf(stderr, "Parse error: %s\n", parser_get_error(parser));
        parser_destroy(parser);
        lexer_destroy(lexer);
        fclose(input_file);
        fclose(output_file);
        return 1;
    }
    
    printf("Parsing completed successfully.\n");
    
    // Optional: Print AST for debugging
    if (getenv("DEBUG_AST")) {
        printf("\nAbstract Syntax Tree:\n");
        ast_print(ast, 0);
        printf("\n");
    }
    
    // Create code generator
    CodeGenerator *codegen = codegen_create(output_file);
    if (!codegen) {
        fprintf(stderr, "Error: Failed to create code generator\n");
        ast_destroy(ast);
        parser_destroy(parser);
        lexer_destroy(lexer);
        fclose(input_file);
        fclose(output_file);
        return 1;
    }
    
    printf("Generating assembly code...\n");
    
    // Generate assembly code
    if (!codegen_generate(codegen, ast)) {
        fprintf(stderr, "Code generation error: %s\n", codegen_get_error(codegen));
        codegen_destroy(codegen);
        ast_destroy(ast);
        parser_destroy(parser);
        lexer_destroy(lexer);
        fclose(input_file);
        fclose(output_file);
        return 1;
    }
    
    printf("Code generation completed successfully.\n");
    printf("Assembly code written to '%s'\n", output_filename);
    
    // Cleanup
    codegen_destroy(codegen);
    ast_destroy(ast);
    parser_destroy(parser);
    lexer_destroy(lexer);
    fclose(input_file);
    fclose(output_file);
    
    printf("\nCompilation successful!\n");
    return 0;
}
