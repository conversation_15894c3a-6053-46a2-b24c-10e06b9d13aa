# SimpleLang Compiler for 8-bit CPU

A minimalistic high-level language compiler that targets an 8-bit CPU architecture. SimpleLang supports basic programming constructs including variable declarations, assignments, arithmetic operations, and conditional statements.

## Features

- **Variable Declarations**: `int` type variables
- **Arithmetic Operations**: Addition (`+`) and subtraction (`-`)
- **Assignment Statements**: Variable assignment with expressions
- **Conditional Statements**: `if` statements with equality comparison (`==`)
- **Code Generation**: Generates assembly code for 8-bit CPU
- **Error Handling**: Comprehensive lexical, syntax, and semantic error reporting

## Language Syntax

### Variable Declaration
```c
int variable_name;
```

### Assignment
```c
variable_name = expression;
```

### Arithmetic Operations
```c
result = a + b;
difference = x - y;
```

### Conditional Statements
```c
if (condition == value) {
    statements;
}
```

## Example Program

```c
// Variable declarations
int a;
int b;
int c;

// Assignments
a = 10;
b = 20;
c = a + b;

// Conditional
if (c == 30) {
    c = c + 1;
}
```

## Building the Compiler

### Prerequisites
- GCC compiler
- Make utility
- Python (for CPU simulator integration)

### Build Instructions
```bash
# Clone the repository (if not already done)
git clone <repository-url>
cd SimpleLang_8-bit_CPU

# Build the compiler
make

# Or build with debug information
make debug
```

## Usage

### Compile a SimpleLang Program
```bash
./simplelang input.sl [output.asm]
```

### Example
```bash
# Compile basic example
./simplelang examples/basic.sl basic.asm

# Compile with conditional
./simplelang examples/conditional.sl conditional.asm
```

### Testing
```bash
# Run built-in tests
make test

# Test with CPU simulator (if available)
make test-cpu
```

## Generated Assembly

The compiler generates assembly code compatible with the 8-bit CPU architecture. Example output:

```assembly
	// SimpleLang compiled program
	.text

	// Declaration: a
	// Assignment: a =
	ldi A 10
	sta %a
	
	// Assignment: b =
	ldi A 20
	sta %b
	
	// Assignment: c =
	lda %a
	mov B A
	lda %b
	add
	sta %c
	
	hlt

	.data
	a = 0
	b = 0
	c = 0
```

## Architecture

The compiler consists of four main components:

### 1. Lexer (`lexer.c`, `lexer.h`)
- Tokenizes SimpleLang source code
- Recognizes keywords, identifiers, operators, and literals
- Handles comments and whitespace

### 2. Parser (`parser.c`, `parser.h`)
- Implements recursive descent parser
- Generates Abstract Syntax Tree (AST)
- Provides comprehensive error reporting

### 3. AST (`ast.c`, `ast.h`)
- Defines AST node structures
- Provides AST creation and manipulation functions
- Supports tree traversal and memory management

### 4. Code Generator (`codegen.c`, `codegen.h`)
- Traverses AST to generate assembly code
- Manages variable symbol table
- Maps high-level constructs to CPU instructions

## CPU Instruction Mapping

| SimpleLang Construct | 8-bit CPU Instructions |
|---------------------|------------------------|
| Variable declaration | Memory allocation in data section |
| Assignment | `ldi`, `lda`, `sta` |
| Addition | `add` |
| Subtraction | `sub` with register swapping |
| Equality comparison | `cmp` |
| Conditional jump | `jz` |

## File Structure

```
SimpleLang_8-bit_CPU/
├── README.md                 # This file
├── SimpleLang_Specification.md  # Language specification
├── Makefile                  # Build configuration
├── compiler.c               # Main compiler program
├── lexer.h, lexer.c         # Lexical analyzer
├── parser.h, parser.c       # Syntax analyzer
├── ast.h, ast.c             # Abstract Syntax Tree
├── codegen.h, codegen.c     # Code generator
├── examples/                # Example programs
│   ├── basic.sl
│   ├── conditional.sl
│   └── arithmetic.sl
└── 8bit-computer/           # 8-bit CPU simulator (submodule)
```

## Error Handling

The compiler provides detailed error messages for:

- **Lexical errors**: Invalid characters, malformed tokens
- **Syntax errors**: Missing semicolons, unmatched braces, invalid expressions
- **Semantic errors**: Undefined variables, type mismatches

Example error output:
```
Parse error: Expected SEMICOLON but got IDENTIFIER at line 5, column 10
```

## Limitations

- No loops (for, while)
- Only integer data type
- Limited arithmetic operations (+ and -)
- Single comparison operator (==)
- No function definitions
- No arrays or complex data structures

## Development

### Debug Mode
```bash
# Build with debug symbols
make debug

# Enable AST debugging
export DEBUG_AST=1
./simplelang program.sl output.asm
```

### Code Analysis
```bash
# Run static analysis (if cppcheck available)
make analyze

# Format code (if clang-format available)
make format
```

## Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure all tests pass

## License

This project is for educational purposes. See the original 8-bit CPU project for its licensing terms.

## References

- [8-bit Computer in Verilog](https://github.com/lightcode/8bit-computer)
- [Ben Eater's 8-bit Computer Series](https://eater.net/8bit/)
- Compiler Design Principles and Techniques
