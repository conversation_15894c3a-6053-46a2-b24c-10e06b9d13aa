#ifndef CODEGEN_H
#define CODEGEN_H

#include "ast.h"
#include <stdio.h>

#define MAX_VARIABLES 256
#define MAX_LABELS 100

// Variable table entry
typedef struct {
    char name[MAX_TOKEN_LEN];
    int memory_address;
    int is_used;
} Variable;

// Label for jumps
typedef struct {
    char name[MAX_TOKEN_LEN];
    int address;
} Label;

// Code generator structure
typedef struct {
    FILE *output;
    Variable variables[MAX_VARIABLES];
    int variable_count;
    int next_memory_address;
    
    Label labels[MAX_LABELS];
    int label_count;
    int next_label_id;
    
    int has_error;
    char error_message[256];
} CodeGenerator;

// Function declarations
CodeGenerator* codegen_create(FILE *output);
void codegen_destroy(CodeGenerator *codegen);
int codegen_generate(CodeGenerator *codegen, ASTNode *ast);

// Internal generation functions
int generate_program(CodeGenerator *codegen, ASTNode *node);
int generate_statement(CodeGenerator *codegen, ASTNode *node);
int generate_declaration(CodeGenerator *codegen, ASTNode *node);
int generate_assignment(CodeGenerator *codegen, ASTNode *node);
int generate_conditional(CodeGenerator *codegen, ASTNode *node);
int generate_expression(CodeGenerator *codegen, ASTNode *node);

// Variable management
int add_variable(CodeGenerator *codegen, const char *name);
int find_variable(CodeGenerator *codegen, const char *name);
int get_variable_address(CodeGenerator *codegen, const char *name);

// Label management
int create_label(CodeGenerator *codegen, char *label_name);
void emit_label(CodeGenerator *codegen, const char *label_name);

// Assembly emission functions
void emit_instruction(CodeGenerator *codegen, const char *instruction);
void emit_instruction_with_operand(CodeGenerator *codegen, const char *instruction, const char *operand);
void emit_instruction_with_address(CodeGenerator *codegen, const char *instruction, int address);
void emit_comment(CodeGenerator *codegen, const char *comment);

// Error handling
void codegen_error(CodeGenerator *codegen, const char *message);
int codegen_has_error(CodeGenerator *codegen);
const char* codegen_get_error(CodeGenerator *codegen);

#endif // CODEGEN_H
